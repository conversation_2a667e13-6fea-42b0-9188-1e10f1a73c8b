@extends('layouts.admin')

@section('title', 'Dashboard - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Start::row-1 -->
    <div class="row">
        <div class="col-xxl-9 col-xl-12">
            <div class="row">
                <div class="col-xl-12">
                    <div class="row">
                        <div class="col-xxl-3 col-lg-6 col-md-6">
                            <div class="card custom-card overflow-hidden">
                                <div class="card-body">
                                    <div class="d-flex align-items-top justify-content-between">
                                        <div>
                                            <span class="avatar avatar-md avatar-rounded bg-primary">
                                                <i class="ti ti-users fs-16"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill ms-3">
                                            <div class="d-flex align-items-center justify-content-between flex-wrap">
                                                <div>
                                                    <p class="text-muted mb-0">Total Users</p>
                                                    <h4 class="fw-semibold mt-1">{{ \App\Models\User::count() }}</h4>
                                                </div>
                                                <div id="crm-total-customers"></div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between mt-1">
                                                <div>
                                                    <a class="text-primary" href="{{ route('admin.users.index') }}">View
                                                        All<i
                                                            class="ti ti-arrow-narrow-right ms-2 fw-semibold d-inline-block"></i></a>
                                                </div>
                                                <div class="text-end">
                                                    <span class="text-success fw-semibold"><i
                                                            class="ri-arrow-up-s-fill me-1"></i>1.6%</span>
                                                    <span class="text-muted op-7 ms-1">this month</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-lg-6 col-md-6">
                            <div class="card custom-card overflow-hidden">
                                <div class="card-body">
                                    <div class="d-flex align-items-top justify-content-between">
                                        <div>
                                            <span class="avatar avatar-md avatar-rounded bg-secondary">
                                                <i class="ti ti-map fs-16"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill ms-3">
                                            <div class="d-flex align-items-center justify-content-between flex-wrap">
                                                <div>
                                                    <p class="text-muted mb-0">Total Fields</p>
                                                    <h4 class="fw-semibold mt-1">{{ \App\Models\Field::count() }}</h4>
                                                </div>
                                                <div id="crm-total-revenue"></div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between mt-1">
                                                <div>
                                                    <a class="text-secondary" href="{{ route('admin.fields.index') }}">View
                                                        All<i
                                                            class="ti ti-arrow-narrow-right ms-2 fw-semibold d-inline-block"></i></a>
                                                </div>
                                                <div class="text-end">
                                                    <span class="text-success fw-semibold"><i
                                                            class="ri-arrow-up-s-fill me-1"></i>3.1%</span>
                                                    <span class="text-muted op-7 ms-1">this month</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-lg-6 col-md-6">
                            <div class="card custom-card overflow-hidden">
                                <div class="card-body">
                                    <div class="d-flex align-items-top justify-content-between">
                                        <div>
                                            <span class="avatar avatar-md avatar-rounded bg-success">
                                                <i class="ti ti-book-bookmark fs-16"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill ms-3">
                                            <div class="d-flex align-items-center justify-content-between flex-wrap">
                                                <div>
                                                    <p class="text-muted mb-0">Total Bookings</p>
                                                    <h4 class="fw-semibold mt-1">{{ \App\Models\Booking::count() }}</h4>
                                                </div>
                                                <div id="crm-conversion-ratio"></div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between mt-1">
                                                <div>
                                                    <a class="text-success" href="{{ route('bookings.index') }}">View All<i
                                                            class="ti ti-arrow-narrow-right ms-2 fw-semibold d-inline-block"></i></a>
                                                </div>
                                                <div class="text-end">
                                                    <span class="text-danger fw-semibold"><i
                                                            class="ri-arrow-down-s-fill me-1"></i>1.4%</span>
                                                    <span class="text-muted op-7 ms-1">this month</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-lg-6 col-md-6">
                            <div class="card custom-card overflow-hidden">
                                <div class="card-body">
                                    <div class="d-flex align-items-top justify-content-between">
                                        <div>
                                            <span class="avatar avatar-md avatar-rounded bg-warning">
                                                <i class="ti ti-calendar-check fs-16"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill ms-3">
                                            <div class="d-flex align-items-center justify-content-between flex-wrap">
                                                <div>
                                                    <p class="text-muted mb-0">My Reservations</p>
                                                    <h4 class="fw-semibold mt-1">
                                                        {{ \App\Models\Reservation::forUser(auth()->id())->active()->count() }}
                                                    </h4>
                                                </div>
                                                <div id="crm-total-deals"></div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between mt-1">
                                                <div>
                                                    <a class="text-warning" href="{{ route('reservations.index') }}">View
                                                        All<i
                                                            class="ti ti-arrow-narrow-right ms-2 fw-semibold d-inline-block"></i></a>
                                                </div>
                                                <div class="text-end">
                                                    <span class="text-info fw-semibold">
                                                        {{ \App\Models\Reservation::forUser(auth()->id())->upcoming()->count() }}
                                                    </span>
                                                    <span class="text-muted op-7 ms-1">upcoming</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xxl-3 col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Welcome Back!
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <span class="avatar avatar-xxl avatar-rounded">
                            <img src="{{ asset('assets/images/faces/9.jpg') }}" alt="">
                        </span>
                        <div class="mt-2">
                            <p class="mb-0 fw-semibold">{{ Auth::user()->name }}</p>
                            <p class="fs-12 op-7 mb-1 text-muted">{{ ucfirst(str_replace('_', ' ', Auth::user()->role)) }}
                            </p>
                            <span class="badge bg-success-transparent">Active</span>
                            <div class="btn-list mt-3">
                                <a href="{{ route('reservations.create') }}" class="btn btn-sm btn-primary btn-wave">
                                    <i class="ti ti-plus me-1"></i>New Reservation
                                </a>
                                <a href="{{ route('reservations.index') }}" class="btn btn-sm btn-success btn-wave">
                                    <i class="ti ti-calendar me-1"></i>My Reservations
                                </a>
                            </div>
                            <div class="col-12">
                                <a href="{{ route('calendar.index') }}" class="btn btn-outline-warning btn-sm w-100">
                                    <i class="ti ti-calendar me-1"></i>Calendar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End::row-1 -->

    <!-- Start::row-1.5 - Reservation Management -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations</div>
                    <a href="{{ route('reservations.index') }}" class="btn btn-info btn-sm">
                        <i class="ti ti-calendar-check me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage property reservations, bookings, and availability.</p>
                    <div class="row gy-2">
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-info btn-sm w-100">
                                <i class="ti ti-list me-1"></i>Reservations
                            </a>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <a href="{{ route('reservations.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-plus me-1"></i>New Reservation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End::row-1.5 -->

    <!-- Start::row-2 -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Recent Bookings
                    </div>
                    <div class="d-flex">
                        <div class="me-3">
                            <input class="form-control form-control-sm" type="text" placeholder="Search Here"
                                aria-label=".form-control-sm example">
                        </div>
                        <div class="dropdown">
                            <a href="javascript:void(0);" class="btn btn-primary btn-sm" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                Sort By<i class="ri-arrow-down-s-line align-middle ms-1 d-inline-block"></i>
                            </a>
                            <ul class="dropdown-menu" role="menu">
                                <li><a class="dropdown-item" href="javascript:void(0);">New</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Popular</a></li>
                                <li><a class="dropdown-item" href="javascript:void(0);">Relevant</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table text-nowrap table-striped table-hover">
                            <thead>
                                <tr>
                                    <th scope="col">Booking ID</th>
                                    <th scope="col">User</th>
                                    <th scope="col">Field</th>
                                    <th scope="col">Date</th>
                                    <th scope="col">Time</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse(\App\Models\Booking::with(['user', 'field'])->latest()->take(5)->get() as $booking)
                                    <tr>
                                        <td>
                                            <div class="fw-semibold">#{{ $booking->id }}</div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="avatar avatar-sm avatar-rounded me-2">
                                                    <img src="{{ asset('assets/images/faces/4.jpg') }}" alt="">
                                                </span>
                                                <div>
                                                    <div class="lh-1">
                                                        <span class="fw-semibold">{{ $booking->user->name }}</span>
                                                    </div>
                                                    <div class="lh-1">
                                                        <span class="fs-11 text-muted">{{ $booking->user->email }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $booking->field->name }}</td>
                                        <td>{{ $booking->booking_date->format('M d, Y') }}</td>
                                        <td>{{ $booking->start_time }} - {{ $booking->end_time }}</td>
                                        <td>
                                            @if ($booking->status === 'confirmed')
                                                <span class="badge bg-success-transparent">Confirmed</span>
                                            @elseif($booking->status === 'pending')
                                                <span class="badge bg-warning-transparent">Pending</span>
                                            @else
                                                <span class="badge bg-danger-transparent">Cancelled</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="hstack gap-2 fs-15">
                                                <a href="{{ route('bookings.show', $booking) }}"
                                                    class="btn btn-icon btn-sm btn-info-transparent rounded-pill"><i
                                                        class="ri-eye-line"></i></a>
                                                <a href="{{ route('bookings.edit', $booking) }}"
                                                    class="btn btn-icon btn-sm btn-primary-transparent rounded-pill"><i
                                                        class="ri-pencil-line"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">No bookings found</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex align-items-center">
                        <div>
                            Showing {{ \App\Models\Booking::count() > 5 ? '5' : \App\Models\Booking::count() }} of
                            {{ \App\Models\Booking::count() }} Entries <i class="bi bi-arrow-right ms-2 fw-semibold"></i>
                        </div>
                        <div class="ms-auto">
                            <a href="{{ route('bookings.index') }}" class="btn btn-primary btn-sm">View All</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End::row-2 -->
@endsection
