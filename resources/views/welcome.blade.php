@extends('layouts.admin')

@section('title', 'SMP Online - Professional Sports Field Booking Platform')

@section('content')
    <!-- Hero Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card overflow-hidden" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-dark) 100%);">
                <div class="card-body text-center py-5">
                    <div class="row justify-content-center">
                        <div class="col-xl-8">
                            <h1 class="display-4 fw-bold text-white mb-3">SMP Online</h1>
                            <p class="lead text-white-50 mb-4">Professional sports field booking and management platform
                                designed for efficient facility administration and seamless user experience.</p>
                            @auth
                                <a href="{{ route('dashboard') }}" class="btn btn-light btn-lg me-3">
                                    <i class="ti ti-dashboard me-2"></i>Go to Dashboard
                                </a>
                                <a href="{{ route('bookings.index') }}" class="btn btn-outline-light btn-lg">
                                    <i class="ti ti-calendar me-2"></i>View Bookings
                                </a>
                            @else
                                <a href="{{ route('login') }}" class="btn btn-light btn-lg">
                                    <i class="ti ti-login me-2"></i>Login to Access System
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Features -->
    <div class="row gy-4 mt-4">
        <div class="col-xl-12">
            <h2 class="fw-semibold mb-4">System Features</h2>
        </div>
        
        <!-- Feature Cards -->
        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-primary-transparent mb-3">
                        <i class="ti ti-building-stadium fs-24 text-primary"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">Field Management</h5>
                    <p class="text-muted mb-0">Comprehensive field administration with detailed information, capacity management, and real-time availability tracking.</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-success-transparent mb-3">
                        <i class="ti ti-calendar-check fs-24 text-success"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">Smart Booking</h5>
                    <p class="text-muted mb-0">Intelligent booking system with conflict detection, automated scheduling, and flexible duration management.</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-warning-transparent mb-3">
                        <i class="ti ti-users fs-24 text-warning"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">User Management</h5>
                    <p class="text-muted mb-0">Role-based access control with administrator, member, and client user permissions and capabilities.</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-info-transparent mb-3">
                        <i class="ti ti-calendar-event fs-24 text-info"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">Calendar Integration</h5>
                    <p class="text-muted mb-0">Interactive calendar interface with drag-and-drop functionality, multiple view modes, and booking visualization.</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-secondary-transparent mb-3">
                        <i class="ti ti-chart-bar fs-24 text-secondary"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">Analytics & Reports</h5>
                    <p class="text-muted mb-0">Comprehensive reporting system with usage statistics, revenue tracking, and performance analytics.</p>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-6">
            <div class="card custom-card h-100">
                <div class="card-body text-center">
                    <div class="avatar avatar-xl avatar-rounded bg-danger-transparent mb-3">
                        <i class="ti ti-device-mobile fs-24 text-danger"></i>
                    </div>
                    <h5 class="fw-semibold mb-2">Mobile Responsive</h5>
                    <p class="text-muted mb-0">Fully responsive design optimized for desktop, tablet, and mobile devices with touch-friendly interfaces.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Roles & Access Levels -->
    <div class="row gy-4 mt-5">
        <div class="col-xl-12">
            <h2 class="fw-semibold mb-4">User Roles & Access Levels</h2>
        </div>

        <!-- Administrator Role -->
        <div class="col-xl-4">
            <div class="card custom-card h-100 border-danger">
                <div class="card-header bg-danger-transparent">
                    <div class="card-title text-danger">
                        <i class="ti ti-crown me-2"></i>Administrator
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="fw-semibold mb-3">Full System Access</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>User management and permissions</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Field creation and configuration</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>All booking operations</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>System settings and configuration</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Analytics and reporting</li>
                        <li class="mb-0"><i class="ti ti-check text-success me-2"></i>Client approval management</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Member Role -->
        <div class="col-xl-4">
            <div class="card custom-card h-100 border-primary">
                <div class="card-header bg-primary-transparent">
                    <div class="card-title text-primary">
                        <i class="ti ti-user me-2"></i>Member
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="fw-semibold mb-3">Standard Booking Access</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Create and manage own bookings</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>View calendar and availability</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Profile management</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Booking history access</li>
                        <li class="mb-2"><i class="ti ti-x text-danger me-2"></i>User management</li>
                        <li class="mb-0"><i class="ti ti-x text-danger me-2"></i>System administration</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Client User Role -->
        <div class="col-xl-4">
            <div class="card custom-card h-100 border-warning">
                <div class="card-header bg-warning-transparent">
                    <div class="card-title text-warning">
                        <i class="ti ti-user-check me-2"></i>Client User
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="fw-semibold mb-3">Limited Access</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Request bookings (requires approval)</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>View approved bookings</li>
                        <li class="mb-2"><i class="ti ti-check text-success me-2"></i>Basic profile access</li>
                        <li class="mb-2"><i class="ti ti-x text-danger me-2"></i>Direct booking creation</li>
                        <li class="mb-2"><i class="ti ti-x text-danger me-2"></i>Calendar management</li>
                        <li class="mb-0"><i class="ti ti-x text-danger me-2"></i>Administrative functions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Getting Started -->
    <div class="row gy-4 mt-5">
        <div class="col-xl-12">
            <h2 class="fw-semibold mb-4">Getting Started</h2>
        </div>

        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="row gy-4">
                        <div class="col-xl-4">
                            <div class="d-flex align-items-start">
                                <div class="avatar avatar-lg avatar-rounded bg-primary text-white me-3">
                                    <span class="fw-bold">1</span>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-2">Contact Administrator</h6>
                                    <p class="text-muted mb-0">Reach out to the system administrator to request access to SMP Online. Provide your contact information and intended use case.</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4">
                            <div class="d-flex align-items-start">
                                <div class="avatar avatar-lg avatar-rounded bg-success text-white me-3">
                                    <span class="fw-bold">2</span>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-2">Receive Credentials</h6>
                                    <p class="text-muted mb-0">The administrator will create your account and provide login credentials with appropriate permissions based on your role and requirements.</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-4">
                            <div class="d-flex align-items-start">
                                <div class="avatar avatar-lg avatar-rounded bg-warning text-white me-3">
                                    <span class="fw-bold">3</span>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-2">Login & Explore</h6>
                                    <p class="text-muted mb-0">Use your credentials to log into the system, explore the dashboard, and begin managing your field bookings and activities.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row gy-4 mt-5">
        <div class="col-xl-12">
            <div class="card custom-card bg-light">
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-3 col-lg-6">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md avatar-rounded bg-primary-transparent me-3">
                                    <i class="ti ti-shield-check text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1">Secure Access</h6>
                                    <p class="text-muted mb-0 fs-12">Role-based authentication</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-lg-6">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md avatar-rounded bg-success-transparent me-3">
                                    <i class="ti ti-clock-24 text-success"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1">24/7 Availability</h6>
                                    <p class="text-muted mb-0 fs-12">Always accessible</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-lg-6">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md avatar-rounded bg-warning-transparent me-3">
                                    <i class="ti ti-device-mobile text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1">Mobile Friendly</h6>
                                    <p class="text-muted mb-0 fs-12">Responsive design</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-lg-6">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-md avatar-rounded bg-info-transparent me-3">
                                    <i class="ti ti-headset text-info"></i>
                                </div>
                                <div>
                                    <h6 class="fw-semibold mb-1">Support Available</h6>
                                    <p class="text-muted mb-0 fs-12">Administrator contact</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
