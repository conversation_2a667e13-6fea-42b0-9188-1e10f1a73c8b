@extends('layouts.admin')

@section('title', 'Amenity Management - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Amenity Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Amenities</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Available Amenities</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.amenities.create') }}" class="btn btn-sm btn-primary">
                            <i class="ri-add-line me-1"></i>Add New Amenity
                        </a>
                        <button type="button" class="btn btn-sm btn-secondary" id="bulkActionBtn" disabled>
                            <i class="ri-settings-3-line me-1"></i>Bulk Actions
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="ri-search-line"></i></span>
                                <input type="text" class="form-control" id="searchInput"
                                    placeholder="Search amenities...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="perPageSelect">
                                <option value="10">10 per page</option>
                                <option value="25">25 per page</option>
                                <option value="50">50 per page</option>
                                <option value="100">100 per page</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-secondary" id="resetFilters">
                                <i class="ri-refresh-line me-1"></i>Reset Filters
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Actions Form -->
                    <form id="bulkActionForm" method="POST" action="{{ route('admin.amenities.bulk-action') }}"
                        style="display: none;">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <select name="action" class="form-select" required>
                                    <option value="">Select Action</option>
                                    <option value="activate">Activate Selected</option>
                                    <option value="deactivate">Deactivate Selected</option>
                                    <option value="delete">Delete Selected</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning">
                                    <i class="ri-play-line me-1"></i>Execute Action
                                </button>
                                <button type="button" class="btn btn-secondary" id="cancelBulkAction">
                                    <i class="ri-close-line me-1"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Amenities Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="amenitiesTable">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th>Icon</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Fields Count</th>
                                    <th>Created</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="amenitiesTableBody">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="booking-pagination">
                        <div class="d-flex justify-content-between align-items-center">
                            <div id="paginationInfo" class="pagination-info"></div>
                            <nav id="paginationNav" class="admin-pagination"></nav>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <div id="loadingSpinner" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Amenity</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="ri-error-warning-line text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p>Are you sure you want to delete this amenity?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Amenity</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentPage = 1;
            let currentSort = 'name';
            let currentDirection = 'asc';

            // Load amenities data
            function loadAmenities() {
                const searchTerm = document.getElementById('searchInput').value;
                const statusFilter = document.getElementById('statusFilter').value;
                const perPage = document.getElementById('perPageSelect').value;

                document.getElementById('loadingSpinner').style.display = 'block';
                document.getElementById('amenitiesTableBody').style.display = 'none';

                const params = new URLSearchParams({
                    page: currentPage,
                    search: searchTerm,
                    status: statusFilter,
                    per_page: perPage,
                    sort: currentSort,
                    direction: currentDirection
                });

                fetch(`{{ route('admin.amenities.index') }}?${params}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        renderTable(data.data);
                        renderPagination(data);
                        document.getElementById('loadingSpinner').style.display = 'none';
                        document.getElementById('amenitiesTableBody').style.display = '';
                    })
                    .catch(error => {
                        console.error('Error loading amenities:', error);
                        document.getElementById('loadingSpinner').style.display = 'none';
                    });
            }

            // Render table rows
            function renderTable(amenities) {
                const tbody = document.getElementById('amenitiesTableBody');
                tbody.innerHTML = '';

                amenities.forEach(amenity => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                <td>
                    <div class="form-check">
                        <input class="form-check-input amenity-checkbox" type="checkbox" value="${amenity.id}">
                    </div>
                </td>
                <td><i class="${amenity.icon_class}"></i></td>
                <td>${amenity.name}</td>
                <td>${amenity.description || 'No description'}</td>
                <td>
                    <span class="badge ${amenity.is_active ? 'bg-success' : 'bg-danger'}">
                        ${amenity.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${amenity.fields_count || 0}</td>
                <td>${new Date(amenity.created_at).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="/admin/amenities/${amenity.id}" class="btn btn-sm btn-info">
                            <i class="ri-eye-line"></i>
                        </a>
                        <a href="/admin/amenities/${amenity.id}/edit" class="btn btn-sm btn-primary">
                            <i class="ri-edit-line"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(${amenity.id})">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                </td>
            `;
                    tbody.appendChild(row);
                });

                updateBulkActionButton();
            }

            // Render pagination
            function renderPagination(data) {
                const info = document.getElementById('paginationInfo');
                const nav = document.getElementById('paginationNav');

                info.textContent = `Showing ${data.from || 0} to ${data.to || 0} of ${data.total} results`;

                // Create pagination links
                let paginationHTML = '<ul class="pagination mb-0">';

                // Previous button
                if (data.prev_page_url) {
                    paginationHTML +=
                        `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${data.current_page - 1})">Previous</a></li>`;
                }

                // Page numbers
                for (let i = Math.max(1, data.current_page - 2); i <= Math.min(data.last_page, data.current_page +
                        2); i++) {
                    paginationHTML += `<li class="page-item ${i === data.current_page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>`;
                }

                // Next button
                if (data.next_page_url) {
                    paginationHTML +=
                        `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${data.current_page + 1})">Next</a></li>`;
                }

                paginationHTML += '</ul>';
                nav.innerHTML = paginationHTML;
            }

            // Change page
            window.changePage = function(page) {
                currentPage = page;
                loadAmenities();
            };

            // Update bulk action button
            function updateBulkActionButton() {
                const checkboxes = document.querySelectorAll('.amenity-checkbox:checked');
                const bulkBtn = document.getElementById('bulkActionBtn');
                bulkBtn.disabled = checkboxes.length === 0;
            }

            // Confirm delete
            window.confirmDelete = function(amenityId) {
                const form = document.getElementById('deleteForm');
                form.action = `/admin/amenities/${amenityId}`;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            };

            // Event listeners
            document.getElementById('searchInput').addEventListener('input', debounce(() => {
                currentPage = 1;
                loadAmenities();
            }, 300));

            document.getElementById('statusFilter').addEventListener('change', () => {
                currentPage = 1;
                loadAmenities();
            });

            document.getElementById('perPageSelect').addEventListener('change', () => {
                currentPage = 1;
                loadAmenities();
            });

            document.getElementById('resetFilters').addEventListener('click', () => {
                document.getElementById('searchInput').value = '';
                document.getElementById('statusFilter').value = '';
                document.getElementById('perPageSelect').value = '10';
                currentPage = 1;
                loadAmenities();
            });

            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.amenity-checkbox');
                checkboxes.forEach(cb => cb.checked = this.checked);
                updateBulkActionButton();
            });

            // Individual checkboxes
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('amenity-checkbox')) {
                    updateBulkActionButton();
                }
            });

            // Bulk action button
            document.getElementById('bulkActionBtn').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('.amenity-checkbox:checked');
                const form = document.getElementById('bulkActionForm');

                // Clear existing hidden inputs
                form.querySelectorAll('input[name="amenities[]"]').forEach(input => input.remove());

                // Add selected amenity IDs
                checkboxes.forEach(cb => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'amenities[]';
                    input.value = cb.value;
                    form.appendChild(input);
                });

                form.style.display = 'block';
            });

            // Cancel bulk action
            document.getElementById('cancelBulkAction').addEventListener('click', function() {
                document.getElementById('bulkActionForm').style.display = 'none';
            });

            // Debounce function
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Initial load
            loadAmenities();
        });
    </script>
@endpush
