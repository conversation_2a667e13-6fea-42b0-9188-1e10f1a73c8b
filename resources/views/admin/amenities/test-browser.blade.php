@extends('layouts.admin')

@section('title', 'Amenity Browser Test - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Amenity Browser Test</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.amenities.index') }}">Amenities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Browser Test</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON>er Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Browser Test Results</div>
                </div>
                <div class="card-body">
                    
                    <!-- Test Results Display -->
                    <div id="testResults" class="mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Running Browser Tests...</h6>
                            <p class="mb-0">Please wait while we test the amenity functionality in your browser.</p>
                        </div>
                    </div>

                    <!-- Quick Test Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Quick Tests</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex flex-wrap gap-2">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="testCreateForm()">
                                            <i class="ri-add-line me-1"></i>Test Create Form
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="testEditForm()">
                                            <i class="ri-edit-line me-1"></i>Test Edit Form
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" onclick="testJavaScript()">
                                            <i class="ri-code-line me-1"></i>Test JavaScript
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" onclick="testCSRF()">
                                            <i class="ri-shield-line me-1"></i>Test CSRF
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm" onclick="runAllTests()">
                                            <i class="ri-play-line me-1"></i>Run All Tests
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Navigation Tests</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex flex-wrap gap-2">
                                        <a href="{{ route('admin.amenities.create') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="ri-external-link-line me-1"></i>Go to Create Form
                                        </a>
                                        @if(\App\Models\Amenity::count() > 0)
                                            <a href="{{ route('admin.amenities.edit', \App\Models\Amenity::first()) }}" class="btn btn-outline-secondary btn-sm">
                                                <i class="ri-external-link-line me-1"></i>Go to Edit Form
                                            </a>
                                        @endif
                                        <a href="{{ route('admin.amenities.index') }}" class="btn btn-outline-info btn-sm">
                                            <i class="ri-external-link-line me-1"></i>Go to Index
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Console Log Display -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Console Log</h6>
                        </div>
                        <div class="card-body">
                            <div id="consoleLog" style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto;">
                                <div class="text-muted">Console output will appear here...</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Console logging override
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

function logToDisplay(message, type = 'log') {
    const consoleDiv = document.getElementById('consoleLog');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : 'text-dark';
    
    if (consoleDiv.children.length === 1 && consoleDiv.children[0].classList.contains('text-muted')) {
        consoleDiv.innerHTML = '';
    }
    
    consoleDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
    consoleDiv.scrollTop = consoleDiv.scrollHeight;
}

console.log = function(...args) {
    originalLog.apply(console, args);
    logToDisplay(args.join(' '), 'log');
};

console.error = function(...args) {
    originalError.apply(console, args);
    logToDisplay(args.join(' '), 'error');
};

console.warn = function(...args) {
    originalWarn.apply(console, args);
    logToDisplay(args.join(' '), 'warn');
};

// Test functions
function testCreateForm() {
    console.log('Testing create form accessibility...');
    
    fetch('{{ route("admin.amenities.create") }}')
        .then(response => {
            if (response.ok) {
                console.log('✅ Create form accessible (Status: ' + response.status + ')');
                updateTestResult('create-form', true, 'Create form loads successfully');
            } else {
                console.error('❌ Create form not accessible (Status: ' + response.status + ')');
                updateTestResult('create-form', false, 'Create form failed to load');
            }
        })
        .catch(error => {
            console.error('❌ Create form test failed: ' + error.message);
            updateTestResult('create-form', false, 'Network error: ' + error.message);
        });
}

function testEditForm() {
    console.log('Testing edit form accessibility...');
    
    // Get first amenity ID for testing
    fetch('{{ route("admin.amenities.index") }}')
        .then(response => response.text())
        .then(html => {
            // Try to find an amenity ID in the response
            const match = html.match(/\/admin\/amenities\/(\d+)\/edit/);
            if (match) {
                const amenityId = match[1];
                console.log('Found amenity ID: ' + amenityId);
                
                fetch(`/admin/amenities/${amenityId}/edit`)
                    .then(response => {
                        if (response.ok) {
                            console.log('✅ Edit form accessible (Status: ' + response.status + ')');
                            updateTestResult('edit-form', true, 'Edit form loads successfully');
                        } else {
                            console.error('❌ Edit form not accessible (Status: ' + response.status + ')');
                            updateTestResult('edit-form', false, 'Edit form failed to load');
                        }
                    })
                    .catch(error => {
                        console.error('❌ Edit form test failed: ' + error.message);
                        updateTestResult('edit-form', false, 'Network error: ' + error.message);
                    });
            } else {
                console.warn('⚠️ No amenities found for edit testing');
                updateTestResult('edit-form', false, 'No amenities available for testing');
            }
        })
        .catch(error => {
            console.error('❌ Edit form test failed: ' + error.message);
            updateTestResult('edit-form', false, 'Failed to check for amenities');
        });
}

function testJavaScript() {
    console.log('Testing JavaScript functionality...');
    
    try {
        // Test basic JavaScript
        const testVar = 'JavaScript is working';
        console.log('✅ Basic JavaScript: ' + testVar);
        
        // Test DOM manipulation
        const testDiv = document.createElement('div');
        testDiv.textContent = 'DOM manipulation works';
        console.log('✅ DOM manipulation: ' + testDiv.textContent);
        
        // Test fetch API
        if (typeof fetch !== 'undefined') {
            console.log('✅ Fetch API available');
        } else {
            console.error('❌ Fetch API not available');
        }
        
        // Test form validation functions (if they exist)
        if (typeof FormData !== 'undefined') {
            console.log('✅ FormData API available');
        } else {
            console.error('❌ FormData API not available');
        }
        
        updateTestResult('javascript', true, 'JavaScript functionality working');
        
    } catch (error) {
        console.error('❌ JavaScript test failed: ' + error.message);
        updateTestResult('javascript', false, 'JavaScript error: ' + error.message);
    }
}

function testCSRF() {
    console.log('Testing CSRF token...');
    
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        console.log('✅ CSRF token found: ' + csrfToken.getAttribute('content').substring(0, 10) + '...');
        updateTestResult('csrf', true, 'CSRF token present');
    } else {
        console.error('❌ CSRF token not found');
        updateTestResult('csrf', false, 'CSRF token missing');
    }
}

function runAllTests() {
    console.log('Running all tests...');
    
    // Clear previous results
    document.getElementById('testResults').innerHTML = `
        <div class="alert alert-info">
            <h6 class="alert-heading">Running All Tests...</h6>
            <div id="test-progress">
                <div id="create-form-test">⏳ Testing create form...</div>
                <div id="edit-form-test">⏳ Testing edit form...</div>
                <div id="javascript-test">⏳ Testing JavaScript...</div>
                <div id="csrf-test">⏳ Testing CSRF...</div>
            </div>
        </div>
    `;
    
    // Run tests with delays
    setTimeout(testCreateForm, 100);
    setTimeout(testEditForm, 500);
    setTimeout(testJavaScript, 1000);
    setTimeout(testCSRF, 1500);
}

function updateTestResult(testName, success, message) {
    const testElement = document.getElementById(testName + '-test');
    if (testElement) {
        const icon = success ? '✅' : '❌';
        testElement.innerHTML = `${icon} ${message}`;
        testElement.className = success ? 'text-success' : 'text-danger';
    }
}

// Auto-run basic tests on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Browser test page loaded successfully');
    console.log('User Agent: ' + navigator.userAgent);
    console.log('Current URL: ' + window.location.href);
    
    // Auto-run JavaScript test
    setTimeout(testJavaScript, 1000);
    setTimeout(testCSRF, 1500);
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection: ' + e.reason);
});
</script>
@endpush
