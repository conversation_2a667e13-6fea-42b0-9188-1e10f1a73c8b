@extends('layouts.admin')

@section('title', 'Edit Amenity - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Edit Amenity - {{ $amenity->name }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.amenities.index') }}">Amenities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Edit Amenity Information</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.amenities.show', $amenity) }}" class="btn btn-sm btn-info">
                            <i class="ri-eye-line me-1"></i>View Amenity
                        </a>
                        <a href="{{ route('admin.amenities.index') }}" class="btn btn-sm btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Amenities
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.amenities.update', $amenity) }}">
                        @csrf
                        @method('PUT')

                        <!-- Amenity Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">Amenity Name <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $amenity->name) }}"
                                required class="form-control @error('name') is-invalid @enderror"
                                placeholder="e.g., Lighting, Parking, WiFi">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea name="description" id="description" rows="4"
                                class="form-control @error('description') is-invalid @enderror"
                                placeholder="Describe the amenity and its features...">{{ old('description', $amenity->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: Provide a detailed description of the amenity.</div>
                        </div>

                        <!-- Icon Class -->
                        <!--<div class="mb-3">
                                        <label for="icon_class" class="form-label">Icon Class <span class="text-danger">*</span></label>
                                        <input type="text" name="icon_class" id="icon_class"
                                            value="{{ old('icon_class', $amenity->icon_class) }}" required
                                            class="form-control @error('icon_class') is-invalid @enderror" placeholder="ri-check-line">
                                        @error('icon_class')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror
                                        <div class="form-text">Use Remix Icons classes (e.g., ri-lightbulb-line, ri-car-line)</div>
                                    </div>-->

                        <!-------------------------------------------------------------------------------------->
                        <div class="mb-3">
                            <label class="form-label">Select Icon <span class="text-danger">*</span></label>

                            <div class="d-flex flex-wrap gap-3 border p-2" id="icon-picker"
                                style="max-height: 300px; overflow-y: auto;">
                                @php
                                    $cssPath = public_path('assets\icon-fonts\RemixIcons\fonts\remixicon.css');
                                    $icons = [];

                                    if (file_exists($cssPath)) {
                                        $css = file_get_contents($cssPath);
                                        preg_match_all('/\.((ri-[a-z0-9\-]+-line))\:before/', $css, $matches);
                                        $icons = array_unique($matches[1]);
                                        sort($icons); // Optional: sort alphabetically
                                    }
                                    $selected = old('icon_class', $amenity->icon_class);
                                @endphp

                                @foreach ($icons as $icon)
                                    <div class="icon-option border rounded p-2 {{ $selected === $icon ? 'border-primary' : '' }}"
                                        style="cursor: pointer;" data-icon="{{ $icon }}">
                                        <i class="{{ $icon }} fs-4"></i>
                                    </div>
                                @endforeach
                            </div>

                            <input type="hidden" name="icon_class" id="icon_class" value="{{ $selected }}">
                            @error('icon_class')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror

                            <div class="form-text">Click an icon to select it.</div>
                        </div>
                        <!-------------------------------------------------------------------------------------->

                        <!-- Status -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <!-- Sends value when checkbox is OFF -->
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active"
                                    value="1" {{ old('is_active', $amenity->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Status
                                </label>
                            </div>
                            <div class="form-text">Active amenities will be available for field assignment.</div>
                        </div>

                        <!-- Usage Statistics -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Usage Statistics</h6>
                            <p class="mb-1">Used by: <strong>{{ $amenity->fields()->count() }} fields</strong></p>
                            <p class="mb-0">Created: <strong>{{ $amenity->created_at->format('M d, Y') }}</strong></p>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Update Amenity
                                        </button>
                                        <a href="{{ route('admin.amenities.show', $amenity) }}" class="btn btn-secondary">
                                            <i class="ri-eye-line me-1"></i>View Amenity
                                        </a>
                                    </div>
                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                        data-bs-target="#deleteModal">
                                        <i class="ri-delete-bin-line me-1"></i>Delete Amenity
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Amenity</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="ri-error-warning-line text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p>Are you sure you want to delete <strong>{{ $amenity->name }}</strong>?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                        @if ($amenity->fields()->count() > 0)
                            <div class="alert alert-warning">
                                <strong>Warning:</strong> This amenity is currently used by
                                {{ $amenity->fields()->count() }} field(s).
                                Deleting it will remove it from all associated fields.
                            </div>
                        @endif
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.amenities.destroy', $amenity) }}" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Amenity</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const name = document.getElementById('name').value.trim();
                const iconClass = document.getElementById('icon_class').value.trim();

                if (!name) {
                    e.preventDefault();
                    alert('Please enter an amenity name.');
                    document.getElementById('name').focus();
                    return;
                }

                if (!iconClass) {
                    e.preventDefault();
                    alert('Please enter an icon class.');
                    document.getElementById('icon_class').focus();
                    return;
                }
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iconOptions = document.querySelectorAll('.icon-option');
            const iconInput = document.getElementById('icon_class');

            iconOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active style
                    iconOptions.forEach(opt => opt.classList.remove('border-primary'));
                    // Add active style
                    this.classList.add('border-primary');
                    // Set hidden input
                    iconInput.value = this.getAttribute('data-icon');
                });
            });
        });
    </script>
@endpush
