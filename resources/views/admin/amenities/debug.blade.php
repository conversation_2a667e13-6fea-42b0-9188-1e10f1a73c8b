@extends('layouts.admin')

@section('title', 'Amenity Creation Debug - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Amenity Creation Debug</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.amenities.index') }}">Amenities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Debug</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Amenity Creation Debug Information</div>
                </div>
                <div class="card-body">
                    
                    <!-- System Status -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">System Status</h6>
                        <ul class="mb-0">
                            <li><strong>Laravel Version:</strong> {{ app()->version() }}</li>
                            <li><strong>PHP Version:</strong> {{ PHP_VERSION }}</li>
                            <li><strong>Database:</strong> {{ config('database.default') }}</li>
                            <li><strong>Current User:</strong> {{ auth()->user()->name }} ({{ auth()->user()->role }})</li>
                            <li><strong>Current Time:</strong> {{ now()->format('Y-m-d H:i:s') }}</li>
                        </ul>
                    </div>

                    <!-- Database Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Database Status</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li><strong>Amenities Count:</strong> {{ \App\Models\Amenity::count() }}</li>
                                        <li><strong>Fields Count:</strong> {{ \App\Models\Field::count() }}</li>
                                        <li><strong>Users Count:</strong> {{ \App\Models\User::count() }}</li>
                                        <li><strong>Table Exists:</strong> 
                                            @if(Schema::hasTable('amenities'))
                                                <span class="badge bg-success">Yes</span>
                                            @else
                                                <span class="badge bg-danger">No</span>
                                            @endif
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Route Status</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li><strong>Create Route:</strong> {{ route('admin.amenities.create') }}</li>
                                        <li><strong>Store Route:</strong> {{ route('admin.amenities.store') }}</li>
                                        <li><strong>Index Route:</strong> {{ route('admin.amenities.index') }}</li>
                                        <li><strong>CSRF Token:</strong> <code>{{ csrf_token() }}</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Form -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Test Amenity Creation Form</h6>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif

                            @if($errors->any())
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('admin.amenities.store') }}" id="debugForm">
                                @csrf
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Amenity Name <span class="text-danger">*</span></label>
                                            <input type="text" name="name" id="name" value="Debug Test Amenity {{ now()->format('His') }}" required
                                                class="form-control @error('name') is-invalid @enderror">
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="icon_class" class="form-label">Icon Class <span class="text-danger">*</span></label>
                                            <input type="text" name="icon_class" id="icon_class" value="ri-debug-line" required
                                                class="form-control @error('icon_class') is-invalid @enderror">
                                            @error('icon_class')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea name="description" id="description" rows="3" class="form-control">This is a debug test amenity created at {{ now()->format('Y-m-d H:i:s') }}</textarea>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                        <label class="form-check-label" for="is_active">Active Status</label>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line me-1"></i>Create Debug Amenity
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="testFormData()">
                                        <i class="ri-bug-line me-1"></i>Test Form Data
                                    </button>
                                    <a href="{{ route('admin.amenities.create') }}" class="btn btn-outline-primary">
                                        <i class="ri-external-link-line me-1"></i>Go to Normal Create Form
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Amenities -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Recent Amenities (Last 5)</h6>
                        </div>
                        <div class="card-body">
                            @php
                                $recentAmenities = \App\Models\Amenity::latest()->take(5)->get();
                            @endphp
                            
                            @if($recentAmenities->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Icon</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentAmenities as $amenity)
                                                <tr>
                                                    <td>{{ $amenity->id }}</td>
                                                    <td>{{ $amenity->name }}</td>
                                                    <td><i class="{{ $amenity->icon_class }}"></i> {{ $amenity->icon_class }}</td>
                                                    <td>
                                                        <span class="badge {{ $amenity->is_active ? 'bg-success' : 'bg-danger' }}">
                                                            {{ $amenity->is_active ? 'Active' : 'Inactive' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $amenity->created_at->format('M d, H:i') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-muted">No amenities found</p>
                            @endif
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function testFormData() {
    const form = document.getElementById('debugForm');
    const formData = new FormData(form);
    
    console.log('=== FORM DATA DEBUG ===');
    for (let [key, value] of formData.entries()) {
        console.log(key + ': ' + value);
    }
    
    alert('Form data logged to console. Check browser developer tools.');
}

// Log any JavaScript errors
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

// Log form submission
document.getElementById('debugForm').addEventListener('submit', function(e) {
    console.log('Form submitted at:', new Date().toISOString());
    console.log('Form action:', this.action);
    console.log('Form method:', this.method);
});
</script>
@endpush
