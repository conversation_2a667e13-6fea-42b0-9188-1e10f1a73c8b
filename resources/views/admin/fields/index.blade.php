@extends('layouts.admin')

@section('title', 'Field Management - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Field Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.fields.index') }}">Admin</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Fields</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ri-check-circle-line me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Fields Table Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Fields List
                        <span class="badge bg-primary ms-2">{{ $fields->total() }} Total</span>
                    </div>
                    <div class="d-flex">
                        <a href="{{ route('admin.fields.create') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i>Add New Field
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ route('admin.fields.index') }}" class="mb-4">
                        <div class="row gy-3">
                            <div class="col-xl-3">
                                <input type="text" name="search" value="{{ request('search') }}"
                                    placeholder="Search fields..." class="form-control">
                            </div>
                            <div class="col-xl-3">
                                <select name="type" class="form-select">
                                    <option value="">All Types</option>
                                    @foreach (\App\Models\Field::getFieldTypes() as $key => $type)
                                        <option value="{{ $key }}"
                                            {{ request('type') === $key ? 'selected' : '' }}>{{ $type }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-3">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    @foreach (\App\Models\Field::getStatuses() as $key => $status)
                                        <option value="{{ $key }}"
                                            {{ request('status') === $key ? 'selected' : '' }}>{{ $status }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-3">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary flex-fill">
                                        <i class="ti ti-search me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('admin.fields.index') }}" class="btn btn-secondary flex-fill">
                                        <i class="ti ti-refresh me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Fields Table -->
                    <div class="table-responsive">
                        <table class="table text-nowrap table-striped table-hover" id="fieldsTable">
                            <thead>
                                <tr>
                                    <th scope="col">Field</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Rate/Hour</th>
                                    <th scope="col">Capacity</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Active Bookings</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($fields as $field)
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $field->name }}</div>
                                                <div class="text-muted fs-12">{{ Str::limit($field->description, 50) }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $field->type }}</span>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $field->formatted_day_rate }}</div>
                                                @if ($field->night_hourly_rate)
                                                    <small class="text-muted">Night:
                                                        {{ $field->formatted_night_rate }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $field->capacity }} people</td>
                                        <td>
                                            @if ($field->status === 'Active')
                                                <span class="badge bg-success">{{ $field->status }}</span>
                                            @elseif($field->status === 'Under Maintenance')
                                                <span class="badge bg-warning">{{ $field->status }}</span>
                                            @else
                                                <span class="badge bg-danger">{{ $field->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $field->active_bookings_count }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.fields.show', $field) }}"
                                                    class="btn btn-sm btn-info" title="View Details">
                                                    <i class="ri-eye-line"></i>
                                                </a>
                                                <a href="{{ route('admin.fields.edit', $field) }}"
                                                    class="btn btn-sm btn-warning" title="Edit Field">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="ri-football-line" style="font-size: 3rem; color: #6c757d;"></i>
                                                <h6 class="mt-2 mb-1">No Fields Found</h6>
                                                <p class="text-muted mb-3">Start by creating your first field to manage
                                                    bookings and reservations.</p>
                                                <a href="{{ route('admin.fields.create') }}" class="btn btn-primary">
                                                    <i class="ri-add-line me-1"></i>Create First Field
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($fields->hasPages())
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="pagination-info">
                                Showing {{ $fields->firstItem() ?? 0 }} to {{ $fields->lastItem() ?? 0 }} of
                                {{ $fields->total() }} fields
                            </div>
                            <div>
                                {{ $fields->withQueryString()->links() }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- DataTables Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
@endsection
