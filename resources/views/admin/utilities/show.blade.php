@extends('layouts.admin')

@section('title', $utility->name . ' - Utility Details - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Utility Details</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.utilities.index') }}">Utilities</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $utility->name }}</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON>er Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ri-check-circle-line me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ri-error-warning-line me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <!-- Utility Information -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="{{ $utility->icon_class ?? 'ri-tools-line' }} me-2"></i>{{ $utility->name }}
                        <span class="badge {{ $utility->status_badge_class }} ms-2">{{ $utility->status_text }}</span>
                    </div>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <a href="{{ route('admin.utilities.edit', $utility) }}" class="btn btn-warning btn-sm">
                                <i class="ri-edit-line me-1"></i>Edit Utility
                            </a>
                            @if ($utility->canBeDeleted())
                                <button type="button" class="btn btn-danger btn-sm"
                                    onclick="confirmDelete({{ $utility->id }}, '{{ $utility->name }}')">
                                    <i class="ri-delete-bin-line me-1"></i>Delete Utility
                                </button>
                            @else
                                <button type="button" class="btn btn-secondary btn-sm"
                                    title="Cannot delete - in use by fields" disabled>
                                    <i class="ri-delete-bin-line me-1"></i>Delete Utility
                                </button>
                            @endif
                            <a href="{{ route('admin.utilities.index') }}" class="btn btn-light btn-sm">
                                <i class="ri-arrow-left-line me-1"></i>Back to Utilities
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-semibold mb-3">Basic Information</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold" style="width: 30%;">Name:</td>
                                    <td>{{ $utility->name }}</td>
                                </tr>

                                <tr>
                                    <td class="fw-semibold">Hourly Rate:</td>
                                    <td>
                                        <span class="fw-semibold text-success">{{ $utility->formatted_hourly_rate }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Status:</td>
                                    <td>
                                        <span class="badge {{ $utility->status_badge_class }}">
                                            {{ $utility->status_text }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Created:</td>
                                    <td>{{ $utility->created_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                                @if ($utility->updated_at != $utility->created_at)
                                    <tr>
                                        <td class="fw-semibold">Last Updated:</td>
                                        <td>{{ $utility->updated_at->format('M d, Y \a\t g:i A') }}</td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-semibold mb-3">Description</h6>
                            @if ($utility->description)
                                <p class="text-muted">{{ $utility->description }}</p>
                            @else
                                <p class="text-muted fst-italic">No description provided.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics and Usage -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Usage Statistics</div>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h4 class="mb-1">{{ $utility->fields_count }}</h4>
                            <p class="text-muted mb-0">Fields Using This Utility</p>
                        </div>
                        <div class="avatar avatar-lg bg-primary-transparent">
                            <i class="ri-building-line fs-18"></i>
                        </div>
                    </div>

                    @if ($utility->fields_count > 0)
                        <div class="progress mb-2" style="height: 6px;">
                            <div class="progress-bar bg-primary" role="progressbar"
                                style="width: {{ min(($utility->fields_count / 10) * 100, 100) }}%"></div>
                        </div>
                        <small class="text-muted">
                            {{ number_format(($utility->fields_count / max(\App\Models\Field::count(), 1)) * 100, 1) }}% of
                            all fields
                        </small>
                    @else
                        <div class="alert alert-info">
                            <i class="ri-information-line me-2"></i>
                            This utility is not currently used by any fields.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Quick Actions</div>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.utilities.edit', $utility) }}" class="btn btn-outline-warning">
                            <i class="ri-edit-line me-2"></i>Edit Utility
                        </a>
                        <!--
                            @if ($utility->is_active)
    <form method="POST" action="{{ route('admin.utilities.update', $utility) }}" class="d-inline">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="name" value="{{ $utility->name }}">
                                    <input type="hidden" name="description" value="{{ $utility->description }}">
                                    <input type="hidden" name="hourly_rate" value="{{ $utility->hourly_rate }}">
                                    <button type="submit" class="btn btn-outline-secondary w-100">
                                        <i class="ri-pause-circle-line me-2"></i>Deactivate Utility
                                    </button>
                                </form>
@else
    <form method="POST" action="{{ route('admin.utilities.update', $utility) }}"
                                    class="d-inline">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="name" value="{{ $utility->name }}">
                                    <input type="hidden" name="description" value="{{ $utility->description }}">
                                    <input type="hidden" name="hourly_rate" value="{{ $utility->hourly_rate }}">
                                    <input type="hidden" name="is_active" value="1">
                                    <button type="submit" class="btn btn-outline-success w-100">
                                        <i class="ri-play-circle-line me-2"></i>Activate Utility
                                    </button>
                                </form>
    @endif -->
                        <a href="{{ route('admin.utilities.create') }}" class="btn btn-outline-primary">
                            <i class="ri-add-line me-2"></i>Create New Utility
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fields Using This Utility -->
    @if ($utility->fields->count() > 0)
        <div class="row">
            <div class="col-xl-12">
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title">Fields Using This Utility</div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Field Name</th>
                                        <th>Type</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($utility->fields as $field)
                                        <tr>
                                            <td>
                                                <div class="fw-semibold">{{ $field->name }}</div>
                                                <small
                                                    class="text-muted">{{ $field->description ? Str::limit($field->description, 50) : 'No description' }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ ucfirst($field->type) }}</span>
                                            </td>
                                            <td>{{ $field->location ?: 'Not specified' }}</td>
                                            <td>
                                                <span class="badge {{ $field->is_active ? 'bg-success' : 'bg-danger' }}">
                                                    {{ $field->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.fields.show', $field) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="ri-eye-line me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the utility "<span id="utilityName"></span>"?</p>
                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Utility</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Delete confirmation function
        function confirmDelete(utilityId, utilityName) {
            document.getElementById('utilityName').textContent = utilityName;
            document.getElementById('deleteForm').action = `/admin/utilities/${utilityId}`;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
@endpush
