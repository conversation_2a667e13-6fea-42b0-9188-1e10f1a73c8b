@extends('layouts.auth')

@section('title', 'Reset Password - SMP Online')

@section('content')
    <div class="my-5 d-flex justify-content-center">
        <a href="{{ route('login') }}">
            <img src="{{ asset('assets/images/brand-logos/desktop-logo.png') }}" alt="logo" class="desktop-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-dark.png') }}" alt="logo" class="desktop-dark">
        </a>
    </div>
    <div class="card custom-card">
        <div class="card-body p-5">
            <p class="h5 fw-semibold mb-2 text-center">Reset Password</p>
            <p class="mb-4 text-muted op-7 fw-normal text-center">Enter your new password below</p>

            <form method="POST" action="{{ route('password.store') }}">
                @csrf

                <!-- Password Reset Token -->
                <input type="hidden" name="token" value="{{ $request->route('token') }}">

                <div class="row gy-3">
                    <div class="col-xl-12">
                        <label for="email" class="form-label text-default">Email Address</label>
                        <input type="email" class="form-control form-control-lg @error('email') is-invalid @enderror"
                            id="email" name="email" value="{{ old('email', $request->email) }}"
                            placeholder="Enter your email address" required autofocus readonly>
                        @error('email')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div class="col-xl-12">
                        <label for="password" class="form-label text-default">New Password</label>
                        <div class="input-group">
                            <input type="password"
                                class="form-control form-control-lg @error('password') is-invalid @enderror" id="password"
                                name="password" placeholder="Enter your new password" required autocomplete="new-password">
                            <button class="btn btn-light" type="button" onclick="createpassword('password',this)">
                                <i class="ri-eye-off-line align-middle"></i>
                            </button>
                            @error('password')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-xl-12">
                        <label for="password_confirmation" class="form-label text-default">Confirm Password</label>
                        <div class="input-group">
                            <input type="password"
                                class="form-control form-control-lg @error('password_confirmation') is-invalid @enderror"
                                id="password_confirmation" name="password_confirmation"
                                placeholder="Confirm your new password" required autocomplete="new-password">
                            <button class="btn btn-light" type="button"
                                onclick="createpassword('password_confirmation',this)">
                                <i class="ri-eye-off-line align-middle"></i>
                            </button>
                            @error('password_confirmation')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-xl-12 d-grid mt-3">
                        <button type="submit" class="btn btn-lg btn-primary">Reset Password</button>
                    </div>
                </div>
            </form>
            <div class="text-center">
                <p class="fs-12 text-muted mt-3">Remember your password? <a href="{{ route('login') }}"
                        class="text-primary">Sign In</a></p>
            </div>
        </div>
    </div>

    <script>
        function createpassword(id, button) {
            var input = document.getElementById(id);
            var icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ri-eye-line align-middle';
            } else {
                input.type = 'password';
                icon.className = 'ri-eye-off-line align-middle';
            }
        }
    </script>
@endsection
