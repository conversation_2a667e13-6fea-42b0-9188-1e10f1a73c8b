@extends('layouts.auth')

@section('title', 'Sign Up - SMP Online')

@section('content')
    <div class="my-5 d-flex justify-content-center">
        <a href="{{ route('login') }}">
            <img src="{{ asset('assets/images/brand-logos/desktop-logo.png') }}" alt="logo" class="desktop-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-dark.png') }}" alt="logo" class="desktop-dark">
        </a>
    </div>
    <div class="card custom-card">
        <div class="card-body p-5">
            <p class="h5 fw-semibold mb-2 text-center">Sign Up</p>
            <p class="mb-4 text-muted op-7 fw-normal text-center">Welcome! Please create your account</p>

            <form method="POST" action="{{ route('register') }}">
                @csrf
                <div class="row gy-3">
                    <div class="col-xl-12">
                        <label for="signup-firstname" class="form-label text-default">Full Name</label>
                        <input type="text" class="form-control form-control-lg @error('name') is-invalid @enderror"
                            id="signup-firstname" name="name" value="{{ old('name') }}"
                            placeholder="Enter your full name" required autofocus autocomplete="name">
                        @error('name')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div class="col-xl-12">
                        <label for="signup-email" class="form-label text-default">Email</label>
                        <input type="email" class="form-control form-control-lg @error('email') is-invalid @enderror"
                            id="signup-email" name="email" value="{{ old('email') }}" placeholder="Enter your email"
                            required autocomplete="username">
                        @error('email')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div class="col-xl-12">
                        <label for="signup-password" class="form-label text-default">Password</label>
                        <div class="input-group">
                            <input type="password"
                                class="form-control form-control-lg @error('password') is-invalid @enderror"
                                id="signup-password" name="password" placeholder="Enter your password" required
                                autocomplete="new-password">
                            <button class="btn btn-light" type="button" onclick="createpassword('signup-password',this)">
                                <i class="ri-eye-off-line align-middle"></i>
                            </button>
                            @error('password')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-xl-12 mb-3">
                        <label for="signup-confirmpassword" class="form-label text-default">Confirm Password</label>
                        <div class="input-group">
                            <input type="password"
                                class="form-control form-control-lg @error('password_confirmation') is-invalid @enderror"
                                id="signup-confirmpassword" name="password_confirmation" placeholder="Confirm your password"
                                required autocomplete="new-password">
                            <button class="btn btn-light" type="button"
                                onclick="createpassword('signup-confirmpassword',this)">
                                <i class="ri-eye-off-line align-middle"></i>
                            </button>
                            @error('password_confirmation')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-xl-12 d-grid mt-3">
                        <button type="submit" class="btn btn-lg btn-primary">Create Account</button>
                    </div>
                </div>
            </form>
            <div class="text-center">
                <p class="fs-12 text-muted mt-3">Already have an account? <a href="{{ route('login') }}"
                        class="text-primary">Sign In</a></p>
            </div>
        </div>
    </div>

    <script>
        function createpassword(id, button) {
            var input = document.getElementById(id);
            var icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ri-eye-line align-middle';
            } else {
                input.type = 'password';
                icon.className = 'ri-eye-off-line align-middle';
            }
        }
    </script>
@endsection
