@extends('layouts.auth')

@section('title', 'Forgot Password - SMP Online')

@section('content')
    <div class="my-5 d-flex justify-content-center">
        <a href="{{ route('login') }}">
            <img src="{{ asset('assets/images/brand-logos/desktop-logo.png') }}" alt="logo" class="desktop-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-dark.png') }}" alt="logo" class="desktop-dark">
        </a>
    </div>
    <div class="card custom-card">
        <div class="card-body p-5">
            <p class="h5 fw-semibold mb-2 text-center">Forgot Password</p>
            <p class="mb-4 text-muted op-7 fw-normal text-center">
                Forgot your password? No problem. Just let us know your email address and we will email you a password reset
                link that will allow you to choose a new one.
            </p>

            <!-- Session Status -->
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('password.email') }}">
                @csrf
                <div class="row gy-3">
                    <div class="col-xl-12">
                        <label for="email" class="form-label text-default">Email Address</label>
                        <input type="email" class="form-control form-control-lg @error('email') is-invalid @enderror"
                            id="email" name="email" value="{{ old('email') }}" placeholder="Enter your email address"
                            required autofocus>
                        @error('email')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div class="col-xl-12 d-grid mt-3">
                        <button type="submit" class="btn btn-lg btn-primary">Email Password Reset Link</button>
                    </div>
                </div>
            </form>
            <div class="text-center">
                <p class="fs-12 text-muted mt-3">Remember your password? <a href="{{ route('login') }}"
                        class="text-primary">Sign In</a></p>
            </div>
        </div>
    </div>
@endsection
