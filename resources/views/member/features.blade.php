@extends('layouts.admin')

@section('title', 'Features - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Features</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Features</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Message -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <h3 class="card-title mb-3">Welcome to the Features Dashboard</h3>
                    <p class="text-muted">
                        @if (auth()->user()->isAdmin())
                            As an administrator, you have access to all system features including advanced management tools.
                        @else
                            As a member, you have access to core features for managing your bookings and viewing system
                            information.
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="row">
        <!-- Booking Management -->
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-primary">
                            <i class="ti ti-calendar fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Booking Management</h5>
                    </div>
                    <p class="text-muted mb-3">Create, view, and manage your field bookings with our interactive calendar
                        system.</p>
                    <div class="btn-list">
                        <a href="{{ route('calendar.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-calendar me-1"></i>Open Calendar
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="ti ti-list me-1"></i>My Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management -->
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-success">
                            <i class="ti ti-user fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Profile Management</h5>
                    </div>
                    <p class="text-muted mb-3">Update your personal information, change password, and manage account
                        settings.</p>
                    <div class="btn-list">
                        <a href="{{ route('profile.edit') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-edit me-1"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-info">
                            <i class="ti ti-info-circle fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">System Information</h5>
                    </div>
                    <p class="text-muted mb-3">View system status, available fields, and booking statistics.</p>
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Your Role:</span>
                            <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', auth()->user()->role)) }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Member Since:</span>
                            <span>{{ auth()->user()->created_at->format('M Y') }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Total Bookings:</span>
                            <span class="badge bg-success">{{ auth()->user()->bookings()->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (auth()->user()->isAdmin())
            <!-- Admin Features -->
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="card custom-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <span class="avatar avatar-md avatar-rounded bg-danger">
                                <i class="ti ti-settings fs-16"></i>
                            </span>
                            <h5 class="fw-semibold mb-0 ms-3">Admin Management</h5>
                        </div>
                        <p class="text-muted mb-3">Access administrative tools for managing users, fields, and system
                            settings.</p>
                        <div class="btn-list">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-danger btn-sm">
                                <i class="ti ti-users me-1"></i>User Management
                            </a>
                            <a href="{{ route('admin.fields.index') }}" class="btn btn-outline-danger btn-sm">
                                <i class="ti ti-map me-1"></i>Field Management
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Statistics -->
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="card custom-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <span class="avatar avatar-md avatar-rounded bg-warning">
                                <i class="ti ti-chart-bar fs-16"></i>
                            </span>
                            <h5 class="fw-semibold mb-0 ms-3">System Statistics</h5>
                        </div>
                        <p class="text-muted mb-3">Overview of system usage and performance metrics.</p>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>Total Users:</span>
                                <span class="badge bg-primary">{{ \App\Models\User::count() }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>Total Fields:</span>
                                <span class="badge bg-secondary">{{ \App\Models\Field::count() }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>Total Bookings:</span>
                                <span class="badge bg-success">{{ \App\Models\Booking::count() }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>Active Bookings:</span>
                                <span class="badge bg-warning">{{ \App\Models\Booking::active()->count() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Quick Actions -->
        <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-secondary">
                            <i class="ti ti-bolt fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Quick Actions</h5>
                    </div>
                    <p class="text-muted mb-3">Frequently used actions and shortcuts for efficient navigation.</p>
                    <div class="btn-list">
                        <a href="{{ route('bookings.create') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Booking
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="ti ti-home me-1"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Need Help?</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
                            <div class="text-center">
                                <span class="avatar avatar-xl avatar-rounded bg-primary-transparent">
                                    <i class="ti ti-help fs-24"></i>
                                </span>
                                <h5 class="fw-semibold mt-3">Documentation</h5>
                                <p class="text-muted">Learn how to use the system effectively</p>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
                            <div class="text-center">
                                <span class="avatar avatar-xl avatar-rounded bg-success-transparent">
                                    <i class="ti ti-mail fs-24"></i>
                                </span>
                                <h5 class="fw-semibold mt-3">Support</h5>
                                <p class="text-muted">Contact our support team for assistance</p>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12">
                            <div class="text-center">
                                <span class="avatar avatar-xl avatar-rounded bg-warning-transparent">
                                    <i class="ti ti-bulb fs-24"></i>
                                </span>
                                <h5 class="fw-semibold mt-3">Tips & Tricks</h5>
                                <p class="text-muted">Discover advanced features and shortcuts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
