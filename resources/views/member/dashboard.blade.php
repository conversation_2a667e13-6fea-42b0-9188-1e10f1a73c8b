@extends('layouts.admin')

@section('title', 'Member Dashboard - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Member Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <h3 class="card-title mb-3">Welcome, {{ auth()->user()->name ?? 'Guest' }}!</h3>
                    <p class="text-muted mb-0">You have member access to the system. Explore the features available to
                        you below.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="row">
        <!-- Reservations Card -->
        <div class="col-xl-4 col-lg-6 col-md-6">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations</div>
                    <a href="{{ route('reservations.index') }}" class="btn btn-info btn-sm">
                        <i class="ti ti-calendar-check me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage property reservations, bookings, and availability.</p>
                    <div class="row gy-2">
                        <div class="col-12">
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-info btn-sm w-100">
                                <i class="ti ti-list me-1"></i>Reservations
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('reservations.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-plus me-1"></i>New Reservation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Card -->
        {{-- <div class="col-xl-4 col-lg-6 col-md-6">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-primary">
                            <i class="ti ti-star fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Available Features</h5>
                    </div>
                    <p class="text-muted mb-3">Access your member features and explore system capabilities.</p>
                    <a href="{{ route('member.features') }}" class="btn btn-primary">
                        <i class="ti ti-star me-1"></i>View Features
                    </a>
                </div>
            </div>
        </div> --}}

        <!-- Profile Card -->
        {{-- <div class="col-xl-4 col-lg-6 col-md-6">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="avatar avatar-md avatar-rounded bg-success">
                            <i class="ti ti-user fs-16"></i>
                        </span>
                        <h5 class="fw-semibold mb-0 ms-3">Your Profile</h5>
                    </div>
                    <p class="text-muted mb-3">Manage your account settings and personal information.</p>
                    <a href="{{ route('profile.edit') }}" class="btn btn-success">
                        <i class="ti ti-edit me-1"></i>Edit Profile
                    </a>
                </div>
            </div>
        </div> --}}
    </div>

    <!-- User Account Information -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Your Account Information</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Role</label>
                                <div class="fw-semibold">
                                    {{ auth()->user() ? ucfirst(str_replace('_', ' ', auth()->user()->role)) : 'User' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email</label>
                                <div class="fw-semibold">{{ auth()->user()->email ?? 'N/A' }}</div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Username</label>
                                <div class="fw-semibold">{{ auth()->user()->username ?? 'Not set' }}</div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Member Since</label>
                                <div class="fw-semibold">{{ auth()->user()->created_at?->format('M d, Y') ?? 'N/A' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
