<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add working hours columns to fields table for Phase 1
        Schema::table('fields', function (Blueprint $table) {
            $table->time('opening_time')->default('08:00')->after('status');
            $table->time('closing_time')->default('23:00')->after('opening_time');
            $table->decimal('min_booking_hours', 4, 1)->default(0.5)->after('closing_time'); // Support half-hour minimum
            $table->decimal('max_booking_hours', 4, 1)->default(8.0)->after('min_booking_hours'); // Support half-hour increments
        });

        // Add indexes for better performance
        Schema::table('bookings', function (Blueprint $table) {
            $table->index(['booking_date', 'start_time', 'end_time'], 'booking_time_range_idx');
            $table->index(['field_id', 'booking_date', 'status'], 'field_date_status_idx');
        });

        // Note: Field types will be handled in the seeder since SQLite doesn't support ENUM modifications
        // The existing field types are sufficient, and we'll add new types via seeder data
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added columns
        Schema::table('fields', function (Blueprint $table) {
            $table->dropColumn(['opening_time', 'closing_time', 'min_booking_hours', 'max_booking_hours']);
        });

        // Remove the added indexes
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropIndex('booking_time_range_idx');
            $table->dropIndex('field_date_status_idx');
        });
    }
};
